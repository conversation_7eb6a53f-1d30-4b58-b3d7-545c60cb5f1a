<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Automation</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <!-- Launch Interface - Shows initially -->
    <div id="launch-interface" class="launch-container">
        <div class="launch-content">
            <div class="launch-icon">🚀</div>
            <h2>LinkedIn Automation</h2>
            <p>Launch LinkedIn automation tools</p>
            <button id="launch-linkedin" class="btn-launch">Open LinkedIn & Start</button>
        </div>
    </div>

    <!-- Main Interface - Shows after LinkedIn opens -->
    <div id="main-interface" class="container hidden">
        <header>
            <h1>LinkedIn Auto Connect</h1>
            <div class="status" id="status">Ready</div>
        </header>

        <!-- Single Tab - Campaigns Only -->
        <nav class="tabs">
            <button class="tab-btn active" data-tab="campaigns">Campaigns</button>
        </nav>

        <!-- Campaigns Tab -->
        <div class="tab-content active" id="campaigns">
            <div class="section">
                <h3>Active Campaigns</h3>
                <div id="campaign-list">
                    <div class="empty-state">No campaigns yet. Create your first campaign!</div>
                </div>
                <button class="btn btn-primary" id="create-campaign">+ New Campaign</button>
            </div>

            <!-- Sales Navigator Section -->
            <div class="section">
                <h3>Sales Navigator</h3>
                <p class="section-description">Collect profiles in real-time from Sales Navigator search results</p>
                <div class="sales-navigator-actions">
                    <button class="btn btn-secondary" id="open-sales-navigator">Open Sales Navigator</button>
                    <button class="btn btn-success" id="start-sales-collection">Start Collection</button>
                    <button class="btn btn-danger" id="stop-sales-collection">Stop Collection</button>
                </div>
                <div class="sales-navigator-status">
                    <div class="status-item">
                        <span>Status:</span>
                        <span id="sales-status">Ready</span>
                    </div>
                    <div class="status-item">
                        <span>Collected:</span>
                        <span id="sales-count">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaign Creation Wizard -->
        <div class="modal" id="campaign-modal">
            <div class="modal-content campaign-wizard">
                <!-- Reusable wizard header template -->
                <div class="wizard-header-template hidden">
                    <button class="back-btn">&larr;</button>
                    <h3></h3>
                    <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                </div>

                <!-- Step 1: Campaign Name -->
                <div class="wizard-step active" id="step-1">
                    <div class="wizard-header">
                        <button class="back-btn hidden">&larr;</button>
                        <h3>Campaign name</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 1 out of 4</div>

                    <div class="form-group">
                        <input type="text" id="campaign-name" placeholder="Enter campaign name" class="campaign-input">
                    </div>

                    <div class="wizard-actions">
                        <button class="btn btn-primary wizard-next" id="next-step-1">NEXT</button>
                    </div>
                </div>

                <!-- Step 2: Add People Options -->
                <div class="wizard-step" id="step-2">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-step-1">&larr;</button>
                        <h3>Add people</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 2 out of 4</div>

                    <div class="add-people-options">
                        <button class="option-btn" id="linkedin-search-option">
                            <div class="option-title">ADD PEOPLE FROM LINKEDIN SEARCH</div>
                        </button>

                        <button class="option-btn" id="sales-navigator-option">
                            <div class="option-title">ADD PEOPLE FROM SALES NAVIGATOR</div>
                        </button>

                        <button class="option-btn" id="network-option">
                            <div class="option-title">ADD PEOPLE FROM MY NETWORK</div>
                        </button>

                        <div class="or-divider">Or</div>

                        <!-- Reusable CSV upload component -->
                        <div class="csv-upload-area" id="csv-upload-main">
                            <div class="upload-text">Have a CSV file with your prospects' LinkedIn profile URLs?</div>
                            <button class="upload-btn" id="csv-upload-btn">Click to import the file</button>
                            <input type="file" id="csv-file-input" accept=".csv" class="hidden">
                            <div class="upload-hint">or drop it here</div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Network Search -->
                <div class="wizard-step" id="step-3-network">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-step-2-from-network">&larr;</button>
                        <h3>Add people from your network</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 3 out of 4</div>

                    <div class="network-search-options">
                        <div class="search-instructions">
                            <p>- Use native LinkedIn search filters to find people from your network. Scroll the filters to see more fields.</p>
                            <p>- Once specified, click "Apply LinkedIn filters" to see the results and then press "Start Collecting People" to collect the people.</p>
                        </div>

                        <div class="search-actions">
                            <button class="btn btn-secondary" id="show-network-filters">SHOW LINKEDIN NETWORK FILTERS</button>
                            <div class="collection-options">
                                <button class="btn btn-primary" id="start-network-multi-page-collecting">COLLECT 1-4 PAGES</button>
                            </div>
                        </div>

                        <div class="or-divider">Or</div>

                        <div class="network-list-option">
                            <h4>Browse Your Connections</h4>
                            <p>View and select from your existing LinkedIn connections</p>
                            <button class="btn btn-secondary" id="browse-connections">BROWSE MY CONNECTIONS</button>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Sales Navigator -->
                <div class="wizard-step" id="step-3-sales-navigator">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-step-2-from-sales-navigator">&larr;</button>
                        <h3>Add people from Sales Navigator</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 3 out of 4</div>

                    <div class="sales-navigator-search-options">
                        <div class="search-instructions">
                            <p>- Use LinkedIn Sales Navigator advanced search filters to find your target prospects.</p>
                            <p>- Apply your filters in Sales Navigator, then click "Start Collecting People" to collect the prospects.</p>
                        </div>

                        <div class="search-actions">
                            <button class="btn btn-secondary" id="show-sales-navigator-filters">OPEN SALES NAVIGATOR SEARCH</button>
                            <div class="collection-options">
                                <button class="btn btn-primary" id="start-sales-navigator-multi-page-collecting">COLLECT 1-4 PAGES</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: LinkedIn Search Filters -->
                <div class="wizard-step" id="step-3-search">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-step-2">&larr;</button>
                        <h3>Add people</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 3 out of 4</div>

                    <!-- Reusable search instructions component -->
                    <div class="search-instructions">
                        <p>- Use native LinkedIn search filters to specify your target audience. Scroll the filters to see more fields.</p>
                        <p>- Once specified, click "Apply LinkedIn filters" to see the results and then press "Start Collecting People" to collect the people.</p>
                    </div>

                    <div class="search-actions">
                        <button class="btn btn-secondary" id="show-filters">SHOW LINKEDIN FILTERS</button>
                        <div class="collection-options">
                            <button class="btn btn-primary" id="start-multi-page-collecting">COLLECT 1-4 PAGES</button>
                        </div>
                    </div>

                    <div class="or-divider">Or</div>

                    <!-- Reference to reusable CSV upload (removed duplicate) -->
                    <div class="csv-upload-reference">
                        <div class="upload-text">Have a CSV file with your prospects' LinkedIn profile URLs?</div>
                        <button class="upload-btn" id="csv-upload-btn-2">Click to import the file</button>
                        <div class="upload-hint">or drop it here</div>
                    </div>
                </div>

                <!-- Step 3: Collection Progress -->
                <div class="wizard-step" id="step-3-collecting">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-search">&larr;</button>
                        <h3>Add people</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 3 out of 4</div>

                    <div class="collection-status">
                        <div class="auto-detection-indicator hidden" id="auto-detection-indicator">
                            <span class="indicator-dot"></span>
                            <span>🔄 Auto-collecting profiles...</span>
                        </div>

                        <div class="page-progress" id="page-progress">
                            <div class="page-info">
                                <span class="current-page">Page: <span id="current-page-number">1</span> / <span id="total-pages">4</span></span>
                                <div class="page-progress-bar">
                                    <div class="page-progress-fill" id="page-progress-fill"></div>
                                </div>
                            </div>
                        </div>

                        <div class="collected-count">
                            <span>Collected: <span id="collected-number">0</span></span>
                            <button class="btn btn-secondary" id="pause-collection">PAUSE</button>
                        </div>

                        <div class="collected-profiles" id="collected-profiles-list"></div>
                        <button class="btn btn-primary hidden" id="next-to-messaging">NEXT</button>
                    </div>
                </div>

                <!-- Step 4: Profile Selection & Message Generation -->
                <div class="wizard-step" id="step-4-messaging">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-collecting">&larr;</button>
                        <h3>Select Profiles & Generate Messages</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 4 out of 4</div>

                    <div class="profile-selection-section">
                        <div class="selection-header">
                            <h4>Select profiles to generate messages for:</h4>
                            <div class="selection-controls">
                                <button class="btn btn-small" id="select-all-step4">Select All</button>
                                <button class="btn btn-small" id="deselect-all-step4">Deselect All</button>
                                <span class="selected-count">Selected: <span id="selected-count-step4">0</span></span>
                            </div>
                        </div>

                        <div class="profiles-selection-list" id="profiles-selection-list">
                            <!-- Profile checkboxes will be added here -->
                        </div>

                        <div class="generation-controls">
                            <button class="btn btn-success" id="skip-to-bulk-send" disabled>
                                🚀 Skip to Bulk Send (Generate + Send)
                            </button>
                        </div>
                        <div class="campaign-creation">
                            <button class="btn btn-primary" id="create-campaign-final">CREATE CAMPAIGN</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer>
            <div class="stats">
                <span>Today: <span id="today-count">0</span> connections</span>
                <span>Total: <span id="total-count">0</span> connections</span>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
