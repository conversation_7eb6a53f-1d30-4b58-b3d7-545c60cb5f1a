
if (window.linkedInAutomationInjected) {
} else {
    window.linkedInAutomationInjected = true;

class LinkedInAutomation {
    constructor() {
        this.isRunning = false;
        this.currentCampaign = null;
        this.actionDelay = 30000;
        this.dailyLimit = 50;
        this.todayCount = 0;
        this.isRealTimeMode = false;
        this.isAutoCollecting = false;
        this.isAutoCollectionEnabled = true;
        this.currentPageCollected = false;
        this.autoProfileObserver = null;
        this.autoCollectionTimeout = null;
        this.processedProfiles = new Set();
        this.init();
    }

    init() {
        chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
            this.handleMessage(message, sendResponse);
        });
        this.setupAutoDetection();
        this.setupAutoPopupDetection();

        // Check for Sales Navigator immediately and on page load
        this.checkForSalesNavigator();

        // Also check when page is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.checkForSalesNavigator(), 1000);
            });
        }

        window.addEventListener('load', () => {
            setTimeout(() => this.checkForSalesNavigator(), 2000);
        });
    }

    setupAutoDetection() {
        if (this.isProfilePage() && this.isAutoCollectionEnabled) {
            setTimeout(() => this.startAutoCollection(), 2000);
        }
        this.setupPageChangeMonitoring();
    }

    isProfilePage() {
        const url = window.location.href;
        if (url.includes('/in/') && !url.includes('/search/')) return false;
        return url.includes('linkedin.com/search/results/people') ||
               url.includes('linkedin.com/search/people') ||
               url.includes('linkedin.com/sales/search/people') ||
               url.includes('linkedin.com/mynetwork') ||
               url.includes('linkedin.com/connections') ||
               (url.includes('linkedin.com') && document.querySelector('.reusable-search__result-container, [data-chameleon-result-urn], .search-result, .entity-result, .artdeco-entity-lockup'));
    }

    checkForSalesNavigator() {
        if (this.isSalesNavigatorSearchPage()) {
            setTimeout(() => {
                this.createFloatingSidebar();
                // Auto-start collection immediately
                setTimeout(() => {
                    this.startRealTimeCollection();
                }, 1000);
            }, 2000);
        }
    }

    isSalesNavigatorSearchPage() {
        const url = window.location.href;
        return url.includes('linkedin.com/sales/search/people') ||
               url.includes('linkedin.com/sales/search/lead');
    }

    createFloatingSidebar() {
        // Remove existing sidebar if present
        if (this.floatingSidebar) {
            this.floatingSidebar.remove();
        }

        // Also remove any existing sidebar by ID
        const existingSidebar = document.getElementById('linkedin-sales-navigator-sidebar');
        if (existingSidebar) {
            existingSidebar.remove();
        }

        this.floatingSidebar = document.createElement('div');
        this.floatingSidebar.id = 'linkedin-sales-navigator-sidebar';
        this.floatingSidebar.innerHTML = `
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <span class="sidebar-icon">🎯</span>
                    Sales Navigator Collector
                </div>
                <button class="sidebar-minimize" onclick="this.parentElement.parentElement.classList.toggle('minimized')">−</button>
            </div>
            <div class="sidebar-content">
                <div class="collection-status">
                    <div class="status-item">
                        <span class="status-label">Status:</span>
                        <span class="status-value" id="collection-status">Collecting...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Profiles:</span>
                        <span class="status-value" id="profile-count">0</span>
                    </div>
                </div>
                <div class="sidebar-actions">
                    <button class="action-btn start-btn" id="start-collection">Start Collection</button>
                    <button class="action-btn stop-btn" id="stop-collection">Stop Collection</button>
                    <button class="action-btn export-btn" id="export-profiles">Export Profiles</button>
                </div>
                <div class="profile-preview" id="profile-preview">
                    <div class="preview-header">Recent Profiles:</div>
                    <div class="preview-list" id="preview-list"></div>
                </div>
            </div>
        `;

        this.applySidebarStyles();
        document.body.appendChild(this.floatingSidebar);
        this.setupSidebarEventListeners();
    }

    applySidebarStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #linkedin-sales-navigator-sidebar {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                border: 1px solid #e1e5e9;
                transition: all 0.3s ease;
            }

            #linkedin-sales-navigator-sidebar.minimized .sidebar-content {
                display: none;
            }

            .sidebar-header {
                background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
                color: white;
                padding: 16px;
                border-radius: 12px 12px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .sidebar-title {
                display: flex;
                align-items: center;
                font-weight: 600;
                font-size: 16px;
            }

            .sidebar-icon {
                margin-right: 8px;
                font-size: 18px;
            }

            .sidebar-minimize {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar-minimize:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .sidebar-content {
                padding: 16px;
            }

            .collection-status {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 16px;
            }

            .status-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
            }

            .status-item:last-child {
                margin-bottom: 0;
            }

            .status-label {
                font-weight: 500;
                color: #666;
            }

            .status-value {
                font-weight: 600;
                color: #0077b5;
            }

            .sidebar-actions {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-bottom: 16px;
            }

            .action-btn {
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 14px;
            }

            .start-btn {
                background: #28a745;
                color: white;
            }

            .start-btn:hover {
                background: #218838;
            }

            .stop-btn {
                background: #dc3545;
                color: white;
            }

            .stop-btn:hover {
                background: #c82333;
            }

            .export-btn {
                background: #0077b5;
                color: white;
            }

            .export-btn:hover {
                background: #005885;
            }

            .profile-preview {
                border-top: 1px solid #e1e5e9;
                padding-top: 16px;
            }

            .preview-header {
                font-weight: 600;
                margin-bottom: 12px;
                color: #333;
            }

            .preview-list {
                max-height: 200px;
                overflow-y: auto;
            }

            .preview-item {
                display: flex;
                align-items: center;
                padding: 8px;
                border-radius: 6px;
                margin-bottom: 4px;
                background: #f8f9fa;
                font-size: 13px;
            }

            .preview-avatar {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background: #0077b5;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 8px;
                font-size: 10px;
                font-weight: 600;
            }

            .preview-info {
                flex: 1;
                overflow: hidden;
            }

            .preview-name {
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .preview-company {
                color: #666;
                font-size: 11px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        `;
        document.head.appendChild(style);
    }

    setupSidebarEventListeners() {
        const startBtn = this.floatingSidebar.querySelector('#start-collection');
        const stopBtn = this.floatingSidebar.querySelector('#stop-collection');
        const exportBtn = this.floatingSidebar.querySelector('#export-profiles');

        startBtn.addEventListener('click', () => this.startRealTimeCollection());
        stopBtn.addEventListener('click', () => this.stopRealTimeCollection());
        exportBtn.addEventListener('click', () => this.exportCollectedProfiles());
    }

    startRealTimeCollection() {
        if (this.isCollectingRealTime) return;

        this.isCollectingRealTime = true;
        this.collectedProfiles = this.collectedProfiles || [];

        // Disable old auto-collection to avoid conflicts
        this.isAutoCollectionEnabled = false;
        if (this.isAutoCollecting) {
            this.stopAutoCollection();
        }

        this.updateSidebarStatus('Collecting...', 'collecting');
        this.setupProfileObserver();
        this.collectCurrentPageProfiles();
    }

    stopRealTimeCollection() {
        this.isCollectingRealTime = false;
        this.updateSidebarStatus('Stopped', 'stopped');

        if (this.profileObserver) {
            this.profileObserver.disconnect();
            this.profileObserver = null;
        }


    }

    setupProfileObserver() {
        if (this.profileObserver) {
            this.profileObserver.disconnect();
        }

        this.profileObserver = new MutationObserver((mutations) => {
            if (!this.isCollectingRealTime) return;

            let hasNewProfiles = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const profileElements = node.querySelectorAll ?
                                node.querySelectorAll('.artdeco-entity-lockup, [data-chameleon-result-urn], .search-results__result-item') :
                                [];

                            if (profileElements.length > 0 ||
                                node.matches && node.matches('.artdeco-entity-lockup, [data-chameleon-result-urn], .search-results__result-item')) {
                                hasNewProfiles = true;
                            }
                        }
                    });
                }
            });

            if (hasNewProfiles) {
                setTimeout(() => this.collectCurrentPageProfiles(), 1000);
            }
        });

        const targetNode = document.querySelector('.search-results-container, .search-results, .artdeco-list') || document.body;
        this.profileObserver.observe(targetNode, {
            childList: true,
            subtree: true
        });
    }

    collectCurrentPageProfiles() {
        if (!this.isCollectingRealTime) return;

        const profiles = this.collectSalesNavigatorProfiles();
        let newProfilesCount = 0;

        profiles.forEach(profile => {
            if (!this.collectedProfiles.some(p => p.url === profile.url)) {
                this.collectedProfiles.push(profile);
                newProfilesCount++;
                this.addProfileToPreview(profile);
            }
        });

        this.updateProfileCount();

        if (newProfilesCount > 0) {
            // Send profiles to background script
            chrome.runtime.sendMessage({
                action: 'profilesCollectedRealTime',
                profiles: this.collectedProfiles,
                source: 'sales-navigator'
            }).catch(() => {
                // Background script not available
            });
        }
    }

    updateSidebarStatus(status, type) {
        const statusElement = this.floatingSidebar?.querySelector('#collection-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `status-value ${type}`;
        }
    }

    updateProfileCount() {
        const countElement = this.floatingSidebar?.querySelector('#profile-count');
        if (countElement) {
            countElement.textContent = this.collectedProfiles.length;
        }
    }

    addProfileToPreview(profile) {
        const previewList = this.floatingSidebar?.querySelector('#preview-list');
        if (!previewList) return;

        const previewItem = document.createElement('div');
        previewItem.className = 'preview-item';

        const initials = profile.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();

        previewItem.innerHTML = `
            <div class="preview-avatar">${initials}</div>
            <div class="preview-info">
                <div class="preview-name">${profile.name}</div>
                <div class="preview-company">${profile.company || profile.title || 'No company'}</div>
            </div>
        `;

        previewList.insertBefore(previewItem, previewList.firstChild);

        // Keep only last 10 profiles in preview
        while (previewList.children.length > 10) {
            previewList.removeChild(previewList.lastChild);
        }
    }

    exportCollectedProfiles() {
        if (this.collectedProfiles.length === 0) {
            alert('No profiles to export!');
            return;
        }

        const csv = this.convertToCSV(this.collectedProfiles);
        this.downloadCSV(csv, `sales-navigator-profiles-${new Date().toISOString().split('T')[0]}.csv`);
    }

    convertToCSV(profiles) {
        const headers = ['Name', 'URL', 'Company', 'Title', 'Location', 'Collected At'];
        const csvContent = [
            headers.join(','),
            ...profiles.map(profile => [
                `"${profile.name || ''}"`,
                `"${profile.url || ''}"`,
                `"${profile.company || ''}"`,
                `"${profile.title || ''}"`,
                `"${profile.location || ''}"`,
                `"${profile.collectedAt || ''}"`
            ].join(','))
        ].join('\n');

        return csvContent;
    }

    downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    setupPageChangeMonitoring() {
        let currentUrl = window.location.href;
        const urlObserver = new MutationObserver(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                setTimeout(() => {
                    // Handle regular profile pages
                    if (this.isProfilePage() && !this.isAutoCollecting && this.isAutoCollectionEnabled) {
                        this.startAutoCollection();
                    }

                    // Handle Sales Navigator pages
                    if (this.isSalesNavigatorSearchPage()) {
                        if (!this.floatingSidebar) {
                            this.createFloatingSidebar();
                        }
                        if (!this.isCollectingRealTime) {
                            setTimeout(() => {
                                this.startRealTimeCollection();
                            }, 1000);
                        }
                    } else if (this.floatingSidebar && !this.isSalesNavigatorSearchPage()) {
                        // Remove sidebar if navigating away from Sales Navigator
                        this.stopRealTimeCollection();
                        this.floatingSidebar.remove();
                        this.floatingSidebar = null;
                    }
                }, 2000);
            }
        });

        urlObserver.observe(document.body, { childList: true, subtree: true });
        window.addEventListener('popstate', () => {
            setTimeout(() => {
                if (this.isProfilePage() && !this.isAutoCollecting && this.isAutoCollectionEnabled) {
                    this.startAutoCollection();
                }

                // Handle Sales Navigator on popstate
                if (this.isSalesNavigatorSearchPage()) {
                    if (!this.floatingSidebar) {
                        this.createFloatingSidebar();
                    }
                    if (!this.isCollectingRealTime) {
                        this.startRealTimeCollection();
                    }
                }
            }, 2000);
        });
    }

    setupAutoPopupDetection() {
        if (document.readyState === 'complete') {
            setTimeout(() => this.showAutoPopup(), 3000);
        } else {
            window.addEventListener('load', () => {
                setTimeout(() => this.showAutoPopup(), 3000);
            });
        }
    }

    showAutoPopup() {
        try {
            this.createAutoPopupNotification();
        } catch (error) {
            console.error('Error showing auto popup:', error);
        }
    }

    createAutoPopupNotification() {
        const existing = document.getElementById('linkedin-auto-popup');
        if (existing) existing.remove();

        const popup = document.createElement('div');
        popup.id = 'linkedin-auto-popup';
        popup.innerHTML = `
            <div style="position: fixed; top: 20px; right: 20px; background: linear-gradient(135deg, #0077b5 0%, #005885 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 119, 181, 0.3); z-index: 10000; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 350px; animation: slideIn 0.5s ease-out;">
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <div style="font-size: 24px; margin-right: 10px;">🚀</div>
                    <div>
                        <div style="font-weight: 600; font-size: 16px;">LinkedIn Automation Ready!</div>
                        <div style="font-size: 14px; opacity: 0.9;">Your automation tools are now active</div>
                    </div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button id="open-automation-popup" style="background: white; color: #0077b5; border: none; padding: 8px 16px; border-radius: 6px; font-weight: 600; cursor: pointer; flex: 1; transition: all 0.2s;">Open Tools</button>
                    <button id="dismiss-popup" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: all 0.2s;">Dismiss</button>
                </div>
            </div>
            <style>@keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }</style>
        `;

        document.body.appendChild(popup);

        popup.querySelector('#open-automation-popup').addEventListener('click', () => {
            this.openExtensionPopup();
            popup.remove();
        });

        popup.querySelector('#dismiss-popup').addEventListener('click', () => popup.remove());

        setTimeout(() => {
            if (popup.parentNode) popup.remove();
        }, 10000);
    }

    openExtensionPopup() {
        try {
            chrome.runtime.sendMessage({ action: 'openPopup' });
        } catch (error) {
            console.error('Error opening popup:', error);
        }
    }

    async startAutoCollection() {
        if (this.isAutoCollecting) return;
        this.isAutoCollecting = true;

        try {
            if (chrome.runtime?.id) {
                chrome.runtime.sendMessage({
                    action: 'autoCollectionStarted',
                    url: window.location.href
                });
            }
        } catch (error) {}

        this.collectAndSendProfiles();
        this.setupContinuousMonitoring();
    }

    async collectAndSendProfiles() {
        const profiles = await this.collectMultiplePages(4);
        if (profiles.length > 0) {
            this.sendProfilesRealTime(profiles);
        }
    }

    setupContinuousMonitoring() {
        const observer = new MutationObserver((mutations) => {
            let hasNewProfiles = false;
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const newProfileCards = node.querySelectorAll ?
                                node.querySelectorAll('.reusable-search__result-container, [data-chameleon-result-urn], .search-result, .entity-result') : [];
                            if (newProfileCards.length > 0) hasNewProfiles = true;
                        }
                    });
                }
            });

            if (hasNewProfiles) {
                clearTimeout(this.autoCollectionTimeout);
                this.autoCollectionTimeout = setTimeout(() => this.collectNewProfilesAuto(), 1500);
            }
        });

        observer.observe(document.body, { childList: true, subtree: true });
        this.autoProfileObserver = observer;
    }

    async collectNewProfilesAuto() {
        if (!this.isAutoCollecting) return;

        const profileCards = document.querySelectorAll('.reusable-search__result-container, [data-chameleon-result-urn], .search-result, .entity-result');
        const newProfiles = [];

        profileCards.forEach((card) => {
            if (card.dataset.autoProcessed) return;
            const profile = this.extractProfileFromCard(card);
            if (profile?.name && profile?.url) {
                newProfiles.push(profile);
                card.dataset.autoProcessed = 'true';
            }
        });

        if (newProfiles.length > 0) this.sendProfilesRealTime(newProfiles);
    }

    stopAutoCollection() {
        this.isAutoCollecting = false;
        if (this.autoProfileObserver) {
            this.autoProfileObserver.disconnect();
            this.autoProfileObserver = null;
        }
        if (this.autoCollectionTimeout) {
            clearTimeout(this.autoCollectionTimeout);
            this.autoCollectionTimeout = null;
        }
    }
    handleMessage(message, sendResponse) {
        if (!message || !message.action) {
            sendResponse({ error: 'Invalid message format' });
            return;
        }

        const actions = {
            startAutomation: () => { this.startAutomation(message.campaign); sendResponse({ success: true }); },
            stopAutomation: () => { this.stopAutomation(); sendResponse({ success: true }); },
            getPageInfo: () => sendResponse(this.getPageInfo()),
            collectProfiles: () => { this.collectProfiles().then(profiles => sendResponse({ profiles })); return true; },
            sendDirectMessage: () => { this.handleDirectMessage(message.message, message.profileName, message.profileUrl); sendResponse({ success: true }); },
            showAutoPopup: () => { this.showAutoPopup(); sendResponse({ success: true }); },
            startRealTimeCollection: () => {
                this.isRealTimeMode = true;
                this.currentPageCollected = false;
                setTimeout(() => {
                    this.collectCurrentPageOnly().then(profiles => {
                        if (profiles.length > 0) {
                            this.sendProfilesRealTime(profiles);
                            this.currentPageCollected = true;
                        } else {
                            const alternativeProfiles = this.extractProfilesAlternative();
                            if (alternativeProfiles.length > 0) {
                                this.sendProfilesRealTime(alternativeProfiles.slice(0, 10));
                            }
                        }
                    }).catch(error => console.error('Error in real-time collection:', error));
                }, 1000);
                sendResponse({ success: true });
                return true;
            },
            startMultiPageCollection: () => {
                this.isRealTimeMode = true;
                const maxPages = message.maxPages || 4;
                setTimeout(() => {
                    this.collectMultiplePages(maxPages).then(profiles => {
                        sendResponse({ success: true, totalProfiles: profiles.length });
                    }).catch(error => {
                        sendResponse({ success: false, error: error.message });
                    });
                }, 1000);
                return true;
            },
            stopRealTimeCollection: () => { this.isRealTimeMode = false; this.currentPageCollected = false; sendResponse({ success: true }); return true; },
            stopAutoCollection: () => { this.stopAutoCollection(); sendResponse({ success: true }); return true; },
            startAutoCollection: () => {
                if (!this.isAutoCollecting && this.isAutoCollectionEnabled) this.startAutoCollection();
                sendResponse({ success: true });
                return true;
            },
            enableAutoCollection: () => {
                this.isAutoCollectionEnabled = true;
                if (this.isProfilePage() && !this.isAutoCollecting) this.startAutoCollection();
                sendResponse({ success: true });
                return true;
            },
            disableAutoCollection: () => { this.isAutoCollectionEnabled = false; this.stopAutoCollection(); sendResponse({ success: true }); return true; },
            searchByCompany: () => { this.searchByCompany(message.companyName).then(result => sendResponse(result)); return true; },
            searchNetwork: () => {
                this.searchNetwork(message.criteria).then(profiles => {
                    sendResponse({ profiles: profiles || [] });
                }).catch(error => {
                    sendResponse({ profiles: [], error: error.message });
                });
                return true;
            },
            startSalesNavigatorCollection: () => {
                if (this.isSalesNavigatorSearchPage()) {
                    if (!this.floatingSidebar) {
                        this.createFloatingSidebar();
                    }
                    this.startRealTimeCollection();
                    sendResponse({ success: true, message: 'Sales Navigator collection started' });
                } else {
                    sendResponse({ success: false, message: 'Not on Sales Navigator search page' });
                }
                return true;
            },
            stopSalesNavigatorCollection: () => {
                this.stopRealTimeCollection();
                sendResponse({ success: true, message: 'Sales Navigator collection stopped' });
                return true;
            },
            getSalesNavigatorProfiles: () => {
                sendResponse({ profiles: this.collectedProfiles || [] });
                return true;
            }
        };

        return actions[message.action] ? actions[message.action]() : sendResponse({ error: 'Unknown action: ' + message.action });
    }
    isSearchResultsPage() {
        return window.location.href.includes('/search/people/') ||
               window.location.href.includes('/search/results/people/') ||
               window.location.href.includes('/sales/search/people');
    }

    startAutomationFromPage() {
        if (this.todayCount >= this.dailyLimit) return;
        this.isRunning = true;
        this.processConnections();
    }

    async processConnections() {
        if (!this.isRunning) return;
        const connectButtons = this.findConnectButtons();
        if (connectButtons.length === 0) {
            this.stopAutomation();
            return;
        }

        for (let i = 0; i < connectButtons.length && this.isRunning; i++) {
            if (this.todayCount >= this.dailyLimit) break;
            const button = connectButtons[i];
            const personInfo = this.extractPersonInfo(button);

            try {
                await this.sendConnectionRequest(button, personInfo);
                this.todayCount++;
                if (i < connectButtons.length - 1) await this.delay(this.actionDelay);
            } catch (error) {
                console.error('Error sending connection request:', error);
            }
        }
        this.stopAutomation();
    }
    findConnectButtons() {
        const selectors = ['button[aria-label*="Connect"]', 'button[data-control-name="connect"]', '.search-result__actions button[aria-label*="Invite"]'];
        const buttons = [];
        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                if ((el.textContent.includes('Connect') || el.getAttribute('aria-label')?.includes('Connect')) && el.offsetParent !== null) {
                    buttons.push(el);
                }
            });
        });
        return buttons;
    }

    extractPersonInfo(connectButton) {
        const resultCard = connectButton.closest('.search-result') || connectButton.closest('.reusable-search__result-container') || connectButton.closest('[data-chameleon-result-urn]');
        let name = 'Unknown', company = '', title = '';

        if (resultCard) {
            const nameElement = resultCard.querySelector('.entity-result__title-text a') || resultCard.querySelector('.search-result__result-link') || resultCard.querySelector('[data-anonymize="person-name"]');
            if (nameElement) name = nameElement.textContent.trim();

            const subtitleElement = resultCard.querySelector('.entity-result__primary-subtitle') || resultCard.querySelector('.search-result__truncate');
            if (subtitleElement) title = subtitleElement.textContent.trim();
        }
        return { name, company, title };
    }
    async sendConnectionRequest(button, personInfo) {
        return new Promise((resolve, reject) => {
            try {
                button.click();
                setTimeout(() => {
                    const sendButton = document.querySelector('button[aria-label*="Send without a note"]') || document.querySelector('button[data-control-name="send_invite"]') || document.querySelector('.send-invite__actions button[aria-label*="Send"]');

                    if (sendButton) {
                        sendButton.click();
                        resolve();
                    } else {
                        const addNoteButton = document.querySelector('button[aria-label*="Add a note"]');
                        if (addNoteButton) {
                            addNoteButton.click();
                            setTimeout(() => this.sendCustomMessage(personInfo, resolve, reject), 1000);
                        } else {
                            reject(new Error('Could not find send button'));
                        }
                    }
                }, 1000);
            } catch (error) {
                reject(error);
            }
        });
    }

    async sendCustomMessage(personInfo, resolve, reject) {
        try {
            const messageTemplate = 'Hi {firstName}, I\'d love to connect with you!';
            const personalizedMessage = this.personalizeMessage(messageTemplate, personInfo);
            const messageTextarea = document.querySelector('#custom-message') || document.querySelector('textarea[name="message"]') || document.querySelector('.send-invite__custom-message textarea');

            if (messageTextarea) {
                messageTextarea.value = personalizedMessage;
                messageTextarea.dispatchEvent(new Event('input', { bubbles: true }));

                setTimeout(() => {
                    const sendButton = document.querySelector('button[aria-label*="Send invitation"]') || document.querySelector('.send-invite__actions button[aria-label*="Send"]');
                    if (sendButton) {
                        sendButton.click();
                        resolve();
                    } else {
                        reject(new Error('Could not find send button for custom message'));
                    }
                }, 500);
            } else {
                reject(new Error('Could not find message textarea'));
            }
        } catch (error) {
            reject(error);
        }
    }

    personalizeMessage(template, personInfo) {
        const firstName = personInfo.name.split(' ')[0];
        const lastName = personInfo.name.split(' ').slice(1).join(' ');
        return template.replace(/{firstName}/g, firstName).replace(/{lastName}/g, lastName).replace(/{fullName}/g, personInfo.name).replace(/{company}/g, personInfo.company).replace(/{title}/g, personInfo.title);
    }
    startAutomation(campaign) {
        this.currentCampaign = campaign;
        this.startAutomationFromPage();
    }

    stopAutomation() {
        this.isRunning = false;
    }

    async closeChatWindow() {
        try {
            const primarySelectors = ['button svg[data-test-icon="close-small"]', 'button .artdeco-button__icon[data-test-icon="close-small"]', '[data-test-icon="close-small"]'];
            const fallbackSelectors = ['.msg-overlay-bubble-header__control--close', '.msg-overlay-bubble-header__control[aria-label*="Close"]', '.msg-overlay-bubble-header button[aria-label*="Close"]', '.msg-overlay-bubble-header .artdeco-button--circle', 'button[aria-label="Close conversation"]', '.msg-overlay-bubble-header button:last-child'];

            for (let attempt = 0; attempt < 5; attempt++) {
                for (const selector of primarySelectors) {
                    const closeIcon = document.querySelector(selector);
                    if (closeIcon) {
                        const closeButton = closeIcon.closest('button');
                        if (closeButton && closeButton.offsetParent !== null && !closeButton.disabled) {
                            closeButton.click();
                            await this.delay(500);
                            return true;
                        }
                    }
                }

                for (const selector of fallbackSelectors) {
                    const closeButton = document.querySelector(selector);
                    if (closeButton && closeButton.offsetParent !== null && !closeButton.disabled) {
                        closeButton.click();
                        await this.delay(500);
                        return true;
                    }
                }
                await this.delay(1000);
            }
            return false;
        } catch (error) {
            console.error('Error closing chat window:', error);
            return false;
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async handleDirectMessage(message) {
        try {
            await this.delay(2000);
            const messageButton = await this.findMessageButton();
            if (messageButton) {
                messageButton.click();
                await this.delay(4000);
                const messageInput = await this.findMessageInput();
                if (!messageInput) return;

                await this.pasteMessageDirectly(messageInput, message);
                await this.delay(1000);
                if (!messageInput.textContent.trim() && !messageInput.value) {
                    await this.pasteUsingClipboard(messageInput, message);
                }
                await this.delay(500);
                if (!messageInput.textContent.trim() && !messageInput.value) {
                    messageInput.textContent = message;
                    messageInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
                await this.clickSendButton();
                await this.delay(2000);
                await this.closeChatWindow();
                await this.delay(1000);
            }
        } catch (error) {}
    }

    async findMessageButton() {
        await this.delay(2000);
        const selectors = ['button[aria-label*="Message"]:not([aria-label*="Send"]):not([aria-label*="Share"])', 'button[data-control-name="message"]', '.pv-s-profile-actions button[aria-label*="Message"]', '.pvs-profile-actions__action button[aria-label*="Message"]', '.message-anywhere-button', 'a[data-control-name="message"]'];

        for (const selector of selectors) {
            const button = document.querySelector(selector);
            if (button) {
                const text = button.textContent.toLowerCase().trim();
                const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
                if (!text.includes('send') && !text.includes('share') && !ariaLabel.includes('send') && !ariaLabel.includes('share') && !ariaLabel.includes('post')) {
                    return button;
                }
            }
        }

        const buttons = document.querySelectorAll('button, a');
        for (const button of buttons) {
            const text = button.textContent.toLowerCase().trim();
            const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
            if ((text === 'message' || ariaLabel.includes('message')) && !ariaLabel.includes('send') && !ariaLabel.includes('share') && !ariaLabel.includes('post') && !text.includes('send') && !text.includes('share') && !text.includes('more')) {
                return button;
            }
        }
        return null;
    }

    async pasteMessageDirectly(messageInput, message) {
        if (!messageInput) return;
        messageInput.focus();
        await this.delay(500);

        if (messageInput.contentEditable === 'true') {
            messageInput.innerHTML = `<p>${message}</p>`;
            const range = document.createRange();
            const selection = window.getSelection();
            const textNode = messageInput.querySelector('p') || messageInput;
            range.selectNodeContents(textNode);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
        } else {
            messageInput.value = message;
        }

        const events = [new Event('focus', { bubbles: true }), new Event('input', { bubbles: true }), new Event('change', { bubbles: true }), new KeyboardEvent('keydown', { bubbles: true, key: 'Enter' }), new KeyboardEvent('keyup', { bubbles: true, key: 'Enter' })];
        for (const event of events) {
            messageInput.dispatchEvent(event);
            await this.delay(100);
        }
        messageInput.focus();
        await this.delay(1000);
    }

    async pasteUsingClipboard(messageInput, message) {
        try {
            messageInput.focus();
            await this.delay(300);
            messageInput.textContent = '';
            await navigator.clipboard.writeText(message);
            await this.delay(200);

            const pasteEvent = new ClipboardEvent('paste', { bubbles: true, cancelable: true, clipboardData: new DataTransfer() });
            pasteEvent.clipboardData.setData('text/plain', message);
            messageInput.dispatchEvent(pasteEvent);
            messageInput.dispatchEvent(new Event('input', { bubbles: true }));
            messageInput.dispatchEvent(new Event('change', { bubbles: true }));
            await this.delay(500);
        } catch (error) {
            messageInput.textContent = message;
            messageInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    async findMessageInput() {
        const selectors = ['.msg-form__contenteditable', '.msg-form__msg-content-container div[contenteditable="true"]', 'div[data-placeholder*="message"]', '.compose-form__message-field', 'div[contenteditable="true"][data-placeholder]', '.msg-form__msg-content-container--scrollable div[contenteditable="true"]', '.msg-form__placeholder + div[contenteditable="true"]', 'div[contenteditable="true"][role="textbox"]', '.msg-form div[contenteditable="true"]'];

        for (let attempt = 0; attempt < 8; attempt++) {
            for (const selector of selectors) {
                const input = document.querySelector(selector);
                if (input && input.isContentEditable && input.offsetParent !== null) return input;
            }

            const allContentEditables = document.querySelectorAll('div[contenteditable="true"]');
            for (const element of allContentEditables) {
                if (element.offsetParent === null) continue;
                const placeholder = element.getAttribute('data-placeholder') || element.getAttribute('aria-label') || element.getAttribute('placeholder');
                if (placeholder && placeholder.toLowerCase().includes('message')) return element;
                const parentContainer = element.closest('.msg-form, .compose-form');
                if (parentContainer) return element;
            }
            await this.delay(1000);
        }
        return null;
    }

    async typeText(element, text) {
        if (element.contentEditable === 'true') {
            element.focus();
            if (!element.textContent.trim()) element.textContent = '';
            element.textContent += text;

            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(element);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);

            element.dispatchEvent(new Event('focus', { bubbles: true }));
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
        } else {
            element.focus();
            element.value += text;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
        }
    }

    async clickSendButton() {
        await this.delay(2000);
        const sendSelectors = ['.msg-form__send-button', 'button[type="submit"]', '.msg-form button[type="submit"]', 'button[data-control-name="send"]', 'button[aria-label*="Send"]:not([aria-label*="options"])', '.compose-form__send-button', '.msg-form__send-btn'];

        for (let attempt = 0; attempt < 10; attempt++) {
            for (const selector of sendSelectors) {
                const button = document.querySelector(selector);
                if (button && !button.disabled && button.offsetParent !== null) {
                    const text = button.textContent.toLowerCase().trim();
                    const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
                    if (!text.includes('options') && !ariaLabel.includes('options')) {
                        button.click();
                        await this.delay(1000);
                        return;
                    }
                }
            }

            const buttons = document.querySelectorAll('button');
            for (const button of buttons) {
                const text = button.textContent.toLowerCase().trim();
                const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
                if (text === 'send' && !text.includes('options') && !ariaLabel.includes('options') && !button.disabled && button.offsetParent !== null && button.offsetWidth > 0 && button.offsetHeight > 0) {
                    button.click();
                    await this.delay(1000);
                    return;
                }
            }
            await this.delay(500);
        }
    }
    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            isSearchPage: this.isSearchResultsPage(),
            connectButtonsCount: this.findConnectButtons().length
        };
    }

    async collectProfiles() {
        const profiles = [];
        if (window.location.href.includes('/mynetwork/')) return this.collectNetworkProfiles();
        if (window.location.href.includes('/sales/search/people')) return this.collectSalesNavigatorProfiles();

        const selectors = ['.reusable-search__result-container', '[data-chameleon-result-urn]', '.search-result', '.entity-result'];
        let profileCards = [];
        for (const selector of selectors) {
            profileCards = document.querySelectorAll(selector);
            if (profileCards.length > 0) break;
        }

        profileCards.forEach((card) => {
            const profile = this.extractProfileFromCard(card);
            if (profile?.name && profile?.url) profiles.push(profile);
        });

        if (profiles.length === 0) {
            const alternativeProfiles = this.extractProfilesAlternative();
            if (Array.isArray(alternativeProfiles) && alternativeProfiles.length > 0) {
                profiles.push(...alternativeProfiles);
            }
        }
        return profiles;
    }

    async collectCurrentPageOnly() {
        const allProfiles = await this.collectProfiles();
        const limitedProfiles = allProfiles.slice(0, 10);
        limitedProfiles.forEach(profile => this.sendProfilesRealTime([profile]));
        return limitedProfiles;
    }

    async collectProfilesWithScrolling() {
        const profiles = [];
        const maxScrollAttempts = 5;
        let scrollAttempts = 0;

        // Initial collection without scrolling
        let searchResults = this.getSearchResultElements();
        searchResults.forEach((card) => {
            if (profiles.length < 20) {
                const profile = this.extractProfileFromCard(card);
                if (profile && profile.name && profile.url) {
                    profile.source = 'network-search';
                    profiles.push(profile);
                }
            }
        });

        // Scroll to load more profiles
        while (scrollAttempts < maxScrollAttempts && profiles.length < 20) {
            scrollAttempts++;
            const initialCount = profiles.length;

            if (scrollAttempts <= 3) {
                // Scroll down to load more content
                window.scrollBy(0, window.innerHeight);
                await this.delay(2000);
                window.scrollTo(0, document.body.scrollHeight);
            } else {
                // Scroll back up
                window.scrollBy(0, -window.innerHeight);
                await this.delay(2000);

                if (scrollAttempts === maxScrollAttempts) {
                    window.scrollTo(0, 0);
                }
            }

            await this.delay(2000);

            // Collect newly loaded profiles
            searchResults = this.getSearchResultElements();
            searchResults.forEach((card) => {
                if (profiles.length < 20) {
                    const profile = this.extractProfileFromCard(card);
                    if (profile && profile.name && profile.url) {
                        const isDuplicate = profiles.some(p => p.url === profile.url);
                        if (!isDuplicate) {
                            profile.source = 'network-search';
                            profiles.push(profile);
                        }
                    }
                }
            });

            // If no new profiles were found, break early
            if (profiles.length === initialCount) {
                break;
            }
        }

        return profiles;
    }

    async collectMultiplePages(maxPages = 4) {
        const allProfiles = [];
        const baseUrl = window.location.href.split('&page=')[0].split('?page=')[0];
        let currentPage = 1;

        try {
            this.sendCollectionStatus(`🚀 Starting collection from ${maxPages} pages...`);

            while (currentPage <= maxPages) {
                this.sendCollectionStatus(`Processing page ${currentPage}`);

                // First scroll and collect profiles from current page
                this.sendCollectionStatus(`Scrolling and collecting from page ${currentPage}`);
                let pageProfiles = [];

                try {
                    pageProfiles = await this.collectProfilesWithScrolling();
                    if (!Array.isArray(pageProfiles)) pageProfiles = [];

                    if (pageProfiles.length === 0) {
                        this.sendCollectionStatus(`Page ${currentPage} completed (no profiles)`);
                    }
                } catch (error) {
                    pageProfiles = [];
                    this.sendCollectionStatus(`Page ${currentPage} failed - skipping`);
                }

                if (Array.isArray(pageProfiles) && pageProfiles.length > 0) {
                    pageProfiles.forEach(profile => {
                        if (profile && typeof profile === 'object') {
                            profile.collectedFromPage = currentPage;
                            profile.collectionTimestamp = new Date().toISOString();
                        }
                    });

                    allProfiles.push(...pageProfiles);
                    this.sendProfilesRealTime(pageProfiles);
                    this.sendCollectionStatus(`Completed page ${currentPage} with ${pageProfiles.length} profiles`);
                }

                // Navigate to next page if not the last page
                if (currentPage < maxPages) {
                    const nextPage = currentPage + 1;
                    this.sendCollectionStatus(`Navigating to page ${nextPage}`);
                    let navigationSuccess = false;
                    let attempts = 0;
                    const maxAttempts = 3;

                    while (!navigationSuccess && attempts < maxAttempts) {
                        attempts++;
                        const clickSuccess = await this.clickPaginationButton(nextPage);

                        if (clickSuccess) {
                            await this.delay(2000);
                            const verifiedPage = this.getCurrentPageNumber();
                            if (verifiedPage === nextPage) {
                                navigationSuccess = true;
                            }
                        }

                        if (!navigationSuccess && attempts < maxAttempts) {
                            await this.delay(2000);
                        }
                    }

                    if (!navigationSuccess) {
                        const pageUrl = this.buildPageUrl(baseUrl, nextPage);
                        window.location.href = pageUrl;
                        await this.waitForPageLoad();
                        await this.delay(3000);
                        await this.waitForSearchResults();

                        const finalVerifiedPage = this.getCurrentPageNumber();
                        if (finalVerifiedPage === nextPage) {
                            navigationSuccess = true;
                        }
                    }

                    if (!navigationSuccess) {
                        this.sendCollectionStatus(`Navigation to page ${nextPage} failed - stopping collection`);
                        break;
                    }
                }

                currentPage++;
                if (currentPage <= maxPages) await this.delay(1000);
            }

            const pagesProcessed = currentPage - 1;
            this.sendCollectionStatus(`All pages completed (${pagesProcessed}/${maxPages})`);
            return allProfiles;

        } catch (error) {
            let errorMessage = 'Collection error occurred';
            if (error.message.includes('iterable') || error.message.includes('Symbol.iterator')) {
                errorMessage = 'Profile data format error - collection stopped';
            } else if (error.message.includes('timeout')) {
                errorMessage = 'Page loading timeout - collection stopped';
            } else {
                errorMessage = `Collection error: ${error.message}`;
            }

            this.sendCollectionStatus(errorMessage);
            return Array.isArray(allProfiles) ? allProfiles : [];
        }
    }

    async clickPaginationButton(pageNumber) {
        try {
            const selectors = [`button[aria-label="Page ${pageNumber}"]`, `button[aria-current="false"][aria-label="Page ${pageNumber}"]`, `.artdeco-pagination__button[aria-label="Page ${pageNumber}"]`, `.artdeco-pagination li button[aria-label="Page ${pageNumber}"]`, `[data-test-pagination-page-btn="${pageNumber}"]`, `.pagination button[aria-label="Page ${pageNumber}"]`, `button[data-test-pagination-page-btn="${pageNumber}"]`];

            let pageButton = null;
            for (const selector of selectors) {
                try {
                    pageButton = document.querySelector(selector);
                    if (pageButton) break;
                } catch (e) {
                    continue;
                }
            }

            if (!pageButton) {
                const allButtons = document.querySelectorAll('button');
                for (const button of allButtons) {
                    const buttonText = button.textContent.trim();
                    const span = button.querySelector('span');
                    const spanText = span ? span.textContent.trim() : '';

                    if (buttonText === pageNumber.toString() || spanText === pageNumber.toString()) {
                        const ariaLabel = button.getAttribute('aria-label');
                        const parentClass = button.parentElement ? button.parentElement.className : '';
                        const isPaginationButton = (ariaLabel && ariaLabel.includes('Page')) || parentClass.includes('pagination') || button.className.includes('pagination') || /^\d+$/.test(buttonText) || /^\d+$/.test(spanText);

                        if (isPaginationButton) {
                            pageButton = button;
                            break;
                        }
                    }
                }
            }

            if (pageButton) {
                pageButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.delay(500);
                pageButton.click();
                await this.delay(3000);
                await this.waitForSearchResults();
                return true;
            } else {
                if (pageNumber > 1) {
                    return await this.clickNextButtonToPage(pageNumber);
                }
                return false;
            }

        } catch (error) {
            console.error(`Error clicking pagination button for page ${pageNumber}:`, error);
            return false;
        }
    }

    getCurrentPageNumber() {
        try {
            // Look for the current page button (aria-current="true")
            const currentPageButton = document.querySelector('button[aria-current="true"]');
            if (currentPageButton) {
                const span = currentPageButton.querySelector('span');
                if (span) {
                    const pageNum = parseInt(span.textContent.trim());
                    if (!isNaN(pageNum)) {
                        return pageNum;
                    }
                }
            }

            // Fallback: check URL for page parameter
            const urlParams = new URLSearchParams(window.location.search);
            const pageParam = urlParams.get('page');
            if (pageParam) {
                const pageNum = parseInt(pageParam);
                if (!isNaN(pageNum)) {
                    return pageNum;
                }
            }

            // Default to page 1 if no page indicator found
            return 1;
        } catch (error) {
            console.error('Error getting current page number:', error);
            return 1;
        }
    }

    async clickNextButtonToPage(targetPage) {
        try {
            const currentPage = this.getCurrentPageNumber();
            console.log(`Current page: ${currentPage}, Target page: ${targetPage}`);

            if (currentPage >= targetPage) {
                console.log('Already on or past target page');
                return true;
            }

            const clicksNeeded = targetPage - currentPage;
            console.log(`Need to click Next button ${clicksNeeded} times`);

            for (let i = 0; i < clicksNeeded; i++) {
                console.log(`Clicking Next button (${i + 1}/${clicksNeeded})`);

                // Find Next button
                const nextButton = this.findNextButton();
                if (!nextButton) {
                    console.log('Next button not found');
                    return false;
                }

                // Click Next button
                nextButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.delay(500);
                nextButton.click();

                // Wait for navigation
                await this.delay(3000);
                await this.waitForSearchResults();

                // Verify we moved to the next page
                const newPage = this.getCurrentPageNumber();
                console.log(`After click ${i + 1}: now on page ${newPage}`);

                if (newPage === targetPage) {
                    console.log(`Successfully reached target page ${targetPage}`);
                    return true;
                }
            }

            return this.getCurrentPageNumber() === targetPage;

        } catch (error) {
            console.error('Error in clickNextButtonToPage:', error);
            return false;
        }
    }

    findNextButton() {
        const nextSelectors = [
            'button[aria-label="Next"]',
            'button[aria-label="Next page"]',
            'button:contains("Next")',
            '.artdeco-pagination__button--next',
            'button[data-test-pagination-page-btn="next"]'
        ];

        for (const selector of nextSelectors) {
            try {
                const button = document.querySelector(selector);
                if (button && !button.disabled) {
                    return button;
                }
            } catch (e) {
                continue;
            }
        }

        // Fallback: look for buttons with "Next" text
        const allButtons = document.querySelectorAll('button');
        for (const button of allButtons) {
            if (button.textContent.toLowerCase().includes('next') && !button.disabled) {
                return button;
            }
        }

        return null;
    }



    buildPageUrl(baseUrl, pageNumber) {
        const separator = baseUrl.includes('?') ? '&' : '?';
        return `${baseUrl}${separator}page=${pageNumber}`;
    }

    async waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve, { once: true });
                // Fallback timeout
                setTimeout(resolve, 5000);
            }
        });
    }

    async waitForSearchResults() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 10;

            const checkForResults = () => {
                attempts++;

                // Check for LinkedIn search result containers
                const searchResults = document.querySelector('.search-results-container') ||
                                    document.querySelector('[data-view-name="search-entity-result-universal-template"]') ||
                                    document.querySelector('.reusable-search__result-container') ||
                                    document.querySelector('.search-result__wrapper') ||
                                    document.querySelector('.entity-result');

                if (searchResults || attempts >= maxAttempts) {
                    console.log(`Search results ${searchResults ? 'found' : 'not found'} after ${attempts} attempts`);
                    resolve();
                } else {
                    setTimeout(checkForResults, 500);
                }
            };

            checkForResults();
        });
    }

    sendCollectionStatus(message) {
        try {
            if (chrome.runtime?.id) {
                chrome.runtime.sendMessage({
                    action: 'collectionStatus',
                    message: message
                }).catch(() => {
                    console.log('Status update:', message);
                });
            }
        } catch (error) {
            console.log('Status update:', message);
        }
    }

    sendProfilesRealTime(profiles) {
        if (!this.isAutoCollectionEnabled || profiles.length === 0) return;

        if (!chrome.runtime?.id) return;

        try {
            chrome.runtime.sendMessage({
                action: 'addProfilesRealTime',
                profiles: profiles
            }).catch(() => {});
        } catch (error) {}
    }

    fixProfileData(profile) {
        if (!profile.name ||
            profile.name.includes('Status is') ||
            profile.name.includes('offline') ||
            profile.name.includes('reachable') ||
            profile.name.length < 3) {

            if (profile.location) {
                const nameMatch = profile.location.match(/^([A-Za-z\s]+?)(?:View|•|\n)/);
                if (nameMatch && nameMatch[1].trim().length > 2) {
                    profile.name = nameMatch[1].trim();
                }


                const titleMatch = profile.location.match(/Full Stack Developer|Software Engineer|Developer|Engineer|Manager|Director|CEO|CTO|VP|President/i);
                if (titleMatch && !profile.title) {
                    profile.title = titleMatch[0];
                }


                const locationMatch = profile.location.match(/([A-Za-z\s]+,\s*[A-Za-z\s]+)(?:\n|$)/);
                if (locationMatch) {
                    const cleanLocation = locationMatch[1].trim();
                    if (cleanLocation.includes(',') && !cleanLocation.includes('View')) {
                        profile.location = cleanLocation;
                    }
                }
            }
        }

        if (profile.title && profile.title.includes('degree connection')) {
            if (profile.location) {
                const titleMatch = profile.location.match(/\n\s*([A-Za-z\s]+(?:Developer|Engineer|Manager|Director|CEO|CTO|VP|President|Analyst|Consultant|Specialist)[A-Za-z\s]*)/i);
                if (titleMatch) {
                    profile.title = titleMatch[1].trim();
                } else {
                    profile.title = '';
                }
            } else {
                profile.title = '';
            }
        }

        if (profile.title && profile.title.includes(' at ') && !profile.company) {
            const parts = profile.title.split(' at ');
            if (parts.length === 2) {
                profile.title = parts[0].trim();
                profile.company = parts[1].trim();
            }
        }
    }

    extractProfilesAlternative() {
        const profiles = [];
        const alternativeSelectors = ['.search-results-container .result-card', '.search-results .search-result__wrapper', '.artdeco-list .artdeco-list__item', '.pvs-list .pvs-list__item'];

        for (const selector of alternativeSelectors) {
            const cards = document.querySelectorAll(selector);
            if (cards.length > 0) {
                cards.forEach(card => {
                    const profile = this.extractProfileFromCard(card);
                    if (profile?.name && profile?.url) profiles.push(profile);
                });
                break;
            }
        }
        return profiles;
    }

    async collectNetworkProfiles() {
        const profiles = [];

        const selectors = [
            '.discover-entity-type-card',
            '.mn-person-card',
            '[data-test-id="person-card"]',
            '.artdeco-entity-lockup',
            '.discover-person-card'
        ];

        let profileCards = [];
        for (const selector of selectors) {
            profileCards = document.querySelectorAll(selector);
            if (profileCards.length > 0) break;
        }

        profileCards.forEach(card => {
            const profile = this.extractProfileFromCard(card, true); // Use unified extraction with network flag
            if (profile?.name && profile?.url) {
                profiles.push(profile);
            }
        });

        return profiles;
    }

    async collectSalesNavigatorProfiles() {
        const profiles = [];

        // Sales Navigator specific selectors
        const selectors = [
            '.artdeco-entity-lockup',
            '[data-chameleon-result-urn]',
            '.search-results__result-item',
            '.result-lockup',
            '.entity-result'
        ];

        let profileCards = [];
        for (const selector of selectors) {
            profileCards = document.querySelectorAll(selector);
            if (profileCards.length > 0) break;
        }

        profileCards.forEach(card => {
            const profile = this.extractSalesNavigatorProfile(card);
            if (profile?.name && profile?.url) {
                profile.source = 'sales-navigator';
                profiles.push(profile);
            }
        });

        return profiles;
    }

    extractSalesNavigatorProfile(card) {
        const profile = {
            name: '',
            url: '',
            company: '',
            title: '',
            location: '',
            industry: '',
            profilePic: '',
            collectedAt: new Date().toISOString(),
            source: 'sales-navigator'
        };

        try {
            // Sales Navigator name and URL extraction
            const nameSelectors = [
                '.artdeco-entity-lockup__title a',
                '.result-lockup__name a',
                'a[href*="/sales/lead/"]',
                'a[href*="/in/"]'
            ];

            let nameElement = null;
            for (const selector of nameSelectors) {
                nameElement = card.querySelector(selector);
                if (nameElement) break;
            }

            if (nameElement) {
                profile.name = nameElement.textContent?.trim() || '';
                profile.url = nameElement.href || '';
            }

            // Company and title extraction
            const titleSelectors = [
                '.artdeco-entity-lockup__subtitle',
                '.result-lockup__highlight-keyword',
                '.entity-result__primary-subtitle'
            ];

            for (const selector of titleSelectors) {
                const titleElement = card.querySelector(selector);
                if (titleElement) {
                    const titleText = titleElement.textContent?.trim() || '';
                    if (titleText.includes(' at ')) {
                        const parts = titleText.split(' at ');
                        profile.title = parts[0]?.trim() || '';
                        profile.company = parts[1]?.trim() || '';
                    } else {
                        profile.title = titleText;
                    }
                    break;
                }
            }

            // Location extraction
            const locationSelectors = [
                '.artdeco-entity-lockup__caption',
                '.result-lockup__misc-item',
                '.entity-result__secondary-subtitle'
            ];

            for (const selector of locationSelectors) {
                const locationElement = card.querySelector(selector);
                if (locationElement) {
                    profile.location = locationElement.textContent?.trim() || '';
                    break;
                }
            }

            // Profile picture extraction
            const imgSelectors = [
                '.artdeco-entity-lockup__image img',
                '.result-lockup__image img',
                '.entity-result__image img'
            ];

            for (const selector of imgSelectors) {
                const imgElement = card.querySelector(selector);
                if (imgElement) {
                    profile.profilePic = imgElement.src || '';
                    break;
                }
            }

        } catch (error) {
            console.error('Error extracting Sales Navigator profile:', error);
        }

        return profile;
    }

    extractProfileFromCard(card, isNetworkPage = false) {
        const profile = {
            name: '',
            url: '',
            company: '',
            title: '',
            location: '',
            industry: '',
            profilePic: '',
            collectedAt: new Date().toISOString()
        };

        try {
            const nameSelectors = isNetworkPage ? [
                'a[href*="/in/"]',
                '.discover-entity-type-card__link',
                '.mn-person-card__link',
                '.artdeco-entity-lockup__title a'
            ] : [
                '.entity-result__title-text a',
                '.search-result__result-link',
                'a[href*="/in/"]',
                '.app-aware-link'
            ];

            let nameLink = null;
            for (const selector of nameSelectors) {
                nameLink = card.querySelector(selector);
                if (nameLink) break;
            }

            if (nameLink) {
                profile.name = this.cleanNameText(nameLink.textContent.trim());
                profile.url = nameLink.href || '';
            } else {
                // Fallback: try to find name and URL separately
                const nameResult = this.findNameInCard(card);
                if (nameResult.name) {
                    profile.name = nameResult.name;
                    profile.url = nameResult.url || card.querySelector('a[href*="/in/"]')?.href || '';
                }
            }

            if (profile.url) {
                if (profile.url.startsWith('/')) {
                    profile.url = 'https://www.linkedin.com' + profile.url;
                }
                if (profile.url.includes('?')) {
                    profile.url = profile.url.split('?')[0];
                }
                profile.url = profile.url.replace(/\/$/, '');
            }

            const imgSelectors = [
                '.entity-result__image img',
                '.presence-entity__image img',
                '.discover-entity-type-card__image img',
                '.mn-person-card__picture img',
                '.artdeco-entity-lockup__image img',
                'img[alt*="profile"]',
                'img[alt*="Photo"]',
                'img[data-ghost-classes]',
                'img[src*="profile"]',
                'img'
            ];

            for (const selector of imgSelectors) {
                const imgElement = card.querySelector(selector);
                if (imgElement?.src &&
                    !imgElement.src.includes('data:image') &&
                    !imgElement.src.includes('ghost') &&
                    imgElement.src.includes('http')) {
                    profile.profilePic = imgElement.src;
                    break;
                }
            }

            const subtitleSelectors = isNetworkPage ? [
                '.discover-entity-type-card__occupation',
                '.mn-person-card__occupation',
                '.artdeco-entity-lockup__subtitle'
            ] : [
                '.entity-result__primary-subtitle',
                '.search-result__truncate',
                '.t-14.t-normal'
            ];

            for (const selector of subtitleSelectors) {
                const subtitleElement = card.querySelector(selector);
                if (subtitleElement) {
                    const subtitle = subtitleElement.textContent.trim();
                    const atIndex = subtitle.toLowerCase().indexOf(' at ');
                    if (atIndex !== -1) {
                        profile.title = subtitle.substring(0, atIndex).trim();
                        profile.company = subtitle.substring(atIndex + 4).trim();
                    } else {
                        profile.title = subtitle;
                    }
                    break;
                }
            }

            const locationSelectors = [
                '.entity-result__secondary-subtitle',
                '[data-anonymize="location"]',
                '.t-12.t-black--light'
            ];

            for (const selector of locationSelectors) {
                const locationElement = card.querySelector(selector);
                if (locationElement) {
                    profile.location = locationElement.textContent.trim();
                    break;
                }
            }

            this.fixProfileData(profile);
            if (!profile.name || !profile.url || !profile.url.includes('/in/')) {

                return null;
            }

            return profile;

        } catch (error) {
            console.error('Error extracting profile data:', error);
            return null;
        }
    }

    // Helper methods to reduce duplication in profile extraction
    cleanNameText(nameText) {
        if (nameText.includes('View') && nameText.includes('profile')) {
            const match = nameText.match(/^(.+?)(?:View|•|\n)/);
            if (match) {
                return match[1].trim();
            }
        }
        return nameText;
    }

    findNameInCard(card) {
        const nameSelectors = [
            'span[aria-hidden="true"]',
            '.t-16.t-black.t-bold',
            '[data-anonymize="person-name"] span',
            '.entity-result__title-text span',
            '.search-result__result-link span',
            '.artdeco-entity-lockup__title span',
            'span.t-16',
            'span.t-bold'
        ];

        for (const selector of nameSelectors) {
            const nameSpan = card.querySelector(selector);
            if (nameSpan && this.isValidNameText(nameSpan.textContent.trim())) {
                const parentLink = nameSpan.closest('a') || card.querySelector('a[href*="/in/"]');
                return {
                    name: nameSpan.textContent.trim(),
                    url: parentLink?.href || ''
                };
            }
        }

        // Final fallback: check all profile links
        const allLinks = card.querySelectorAll('a[href*="/in/"]');
        for (const link of allLinks) {
            const text = link.textContent.trim();
            if (this.isValidNameText(text) && text.split(' ').length >= 2) {
                return { name: text, url: link.href };
            }
        }

        return { name: '', url: '' };
    }

    isValidNameText(text) {
        return text && text.length > 2 &&
               !text.includes('Status') &&
               !text.includes('View') &&
               !text.includes('•');
    }

    async searchByCompany(companyName) {
        try {
            const searchUrl = `https://www.linkedin.com/search/results/people/?keywords=${encodeURIComponent(companyName)}&origin=GLOBAL_SEARCH_HEADER`;
            window.location.href = searchUrl;

            return { success: true, message: `Searching for employees at ${companyName}` };
        } catch (error) {
            console.error('Error searching by company:', error);
            return { success: false, message: error.message };
        }
    }

    async searchNetwork(criteria) {
        try {

            const profiles = [];
            let scrollAttempts = 0;
            const maxScrollAttempts = 5;

            this.setupContinuousMonitoring();

            if (criteria.type === 'sales-navigator' || window.location.href.includes('sales/search/people')) {

                let searchResults = this.getSalesNavigatorResultElements();

                searchResults.forEach((card) => {
                    if (profiles.length < 20) {
                        const profile = this.extractSalesNavigatorProfile(card);
                        if (profile && profile.name && profile.url) {
                            profile.source = 'sales-navigator';
                            profiles.push(profile);
                        }
                    }
                });

                while (scrollAttempts < maxScrollAttempts && profiles.length < 20) {
                    scrollAttempts++;
                    const initialCount = profiles.length;

                    if (scrollAttempts <= 3) {
                        window.scrollBy(0, window.innerHeight);
                        await this.delay(2000);
                        window.scrollTo(0, document.body.scrollHeight);
                    } else {
                        window.scrollBy(0, -window.innerHeight);
                        await this.delay(2000);
                        if (scrollAttempts === maxScrollAttempts) {
                            window.scrollTo(0, 0);
                        }
                    }

                    await this.delay(2000);
                    searchResults = this.getSalesNavigatorResultElements();
                    searchResults.forEach((card) => {
                        if (profiles.length < 20) {
                            const profile = this.extractSalesNavigatorProfile(card);
                            if (profile && profile.name && profile.url) {
                                const isDuplicate = profiles.some(p => p.url === profile.url);
                                if (!isDuplicate) {
                                    profile.source = 'sales-navigator';
                                    profiles.push(profile);
                                }
                            }
                        }
                    });

                    const newProfilesCount = profiles.length - initialCount;
                    if (newProfilesCount === 0 && scrollAttempts >= 2) {
                        break;
                    }
                }

            } else if (criteria.type === 'search' || window.location.href.includes('search/results/people')) {

                let searchResults = this.getSearchResultElements();

                searchResults.forEach((card) => {
                    if (profiles.length < 20) {
                        const profile = this.extractProfileFromCard(card);
                        if (profile && profile.name && profile.url) {
                            profile.source = 'network-search';
                            profiles.push(profile);

                        }
                    }
                });

                while (scrollAttempts < maxScrollAttempts && profiles.length < 20) {
                    scrollAttempts++;

                    const initialCount = profiles.length;

                    if (scrollAttempts <= 3) {
                        window.scrollBy(0, window.innerHeight);
                        await this.delay(2000);
                        window.scrollTo(0, document.body.scrollHeight);

                    } else {
                        window.scrollBy(0, -window.innerHeight);
                        await this.delay(2000);

                        if (scrollAttempts === maxScrollAttempts) {
                            window.scrollTo(0, 0);

                        }
                    }

                    await this.delay(2000);
                    searchResults = this.getSearchResultElements();
                    searchResults.forEach((card) => {
                        if (profiles.length < 20) {
                            const profile = this.extractProfileFromCard(card);
                            if (profile && profile.name && profile.url) {
                                const isDuplicate = profiles.some(p => p.url === profile.url);
                                if (!isDuplicate) {
                                    profile.source = 'network-search';
                                    profiles.push(profile);

                                }
                            }
                        }
                    });

                    const newProfilesCount = profiles.length - initialCount;

                    if (newProfilesCount === 0 && scrollAttempts >= 2) {

                        break;
                    }
                }

            } else if (criteria.type === 'connections' || window.location.href.includes('mynetwork') || window.location.href.includes('connections')) {
                let connectionCards = document.querySelectorAll('.mn-connection-card');
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('.connection-card');
                }
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('[data-control-name="connection_profile"]');
                }
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('.artdeco-entity-lockup');
                }
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('li');
                }



                connectionCards.forEach((card, index) => {
                    if (index < 20) { // Process more profiles for better real-time experience
                        const profile = this.extractProfileFromCard(card, true); // Use unified extraction
                        if (profile?.name && profile?.url) {
                            profile.source = 'connections';
                            profiles.push(profile);

                            if (profiles.length <= 3 || profiles.length % 2 === 0) {
                                this.sendProfilesRealTime([profile]);
                            }
                        }
                    }
                });
            }

            return profiles;
        } catch (error) {
            console.error('Error searching network:', error);
            return [];
        }
    }

    getSearchResultElements() {
        const selectors = ['.search-result', '.reusable-search__result-container', '[data-chameleon-result-urn]', 'li[data-reusable-search-result]', '.entity-result'];

        for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) return elements;
        }

        const elements = document.querySelectorAll('li');
        return Array.from(elements).filter(li => li.querySelector('a[href*="/in/"]') || li.querySelector('a[href*="linkedin.com/in/"]'));
    }

    getSalesNavigatorResultElements() {
        const selectors = [
            '.artdeco-entity-lockup',
            '[data-chameleon-result-urn]',
            '.search-results__result-item',
            '.result-lockup',
            '.entity-result',
            'li[data-test-result-item]'
        ];

        for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) return elements;
        }

        // Fallback for Sales Navigator
        const elements = document.querySelectorAll('li');
        return Array.from(elements).filter(li =>
            li.querySelector('a[href*="/sales/lead/"]') ||
            li.querySelector('a[href*="/in/"]') ||
            li.querySelector('.artdeco-entity-lockup')
        );
    }

}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.linkedInAutomation = new LinkedInAutomation();
    });
} else {
    window.linkedInAutomation = new LinkedInAutomation();
}



}
