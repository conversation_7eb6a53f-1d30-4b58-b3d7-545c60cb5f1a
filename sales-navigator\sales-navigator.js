// Sales Navigator Profile Collection - Fresh Implementation
class SalesNavigatorCollector {
    constructor() {
        this.collectedProfiles = [];
        this.isCollecting = false;
        this.currentPage = 1;
        this.totalPages = 1;
        this.init();
    }

    init() {
        // Only run on Sales Navigator pages
        if (this.isSalesNavigatorPage()) {
            this.waitForPageLoad().then(() => {
                this.createCollectionUI();
            });
        }
    }

    isSalesNavigatorPage() {
        return window.location.href.includes('linkedin.com/sales/search/people');
    }

    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkForContent = () => {
                const hasResults = document.querySelector('[data-anonymize="person-name"], .artdeco-entity-lockup, .search-result');
                const hasFilters = document.querySelector('.search-filters-bar, .artdeco-pill');
                
                if (hasResults || hasFilters) {
                    resolve();
                } else {
                    setTimeout(checkForContent, 1000);
                }
            };
            checkForContent();
        });
    }

    createCollectionUI() {
        // Remove existing UI if present
        const existingUI = document.getElementById('sales-nav-collector');
        if (existingUI) existingUI.remove();

        const collectorUI = document.createElement('div');
        collectorUI.id = 'sales-nav-collector';
        collectorUI.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 350px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.15);
                z-index: 999999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                border: 1px solid #e1e5e9;
            ">
                <!-- Header -->
                <div style="
                    background: linear-gradient(135deg, #0a66c2 0%, #004182 100%);
                    color: white;
                    padding: 16px 20px;
                    border-radius: 12px 12px 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 18px;">🎯</span>
                        <span style="font-weight: 600; font-size: 16px;">Sales Navigator Collector</span>
                    </div>
                    <button id="close-collector" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 4px;
                        border-radius: 4px;
                        opacity: 0.8;
                    ">×</button>
                </div>

                <!-- Status -->
                <div style="padding: 16px 20px; border-bottom: 1px solid #e1e5e9;">
                    <div id="collection-status" style="
                        font-size: 14px;
                        color: #666;
                        margin-bottom: 8px;
                    ">Ready to collect profiles</div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: 600; color: #333;">Profiles Found:</span>
                        <span id="profile-count" style="
                            background: #0a66c2;
                            color: white;
                            padding: 4px 12px;
                            border-radius: 20px;
                            font-weight: 600;
                            font-size: 14px;
                        ">0</span>
                    </div>
                </div>

                <!-- Controls -->
                <div style="padding: 16px 20px;">
                    <button id="start-collection" style="
                        width: 100%;
                        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                        color: white;
                        border: none;
                        padding: 12px 16px;
                        border-radius: 8px;
                        font-weight: 600;
                        cursor: pointer;
                        margin-bottom: 12px;
                        font-size: 14px;
                        transition: all 0.2s;
                    ">🚀 Start Collection</button>

                    <button id="stop-collection" style="
                        width: 100%;
                        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                        color: white;
                        border: none;
                        padding: 12px 16px;
                        border-radius: 8px;
                        font-weight: 600;
                        cursor: pointer;
                        margin-bottom: 12px;
                        font-size: 14px;
                        display: none;
                    ">⏹️ Stop Collection</button>

                    <div style="display: flex; gap: 8px;">
                        <button id="export-profiles" style="
                            flex: 1;
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 10px 12px;
                            border-radius: 6px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 13px;
                        ">📥 Export</button>
                        
                        <button id="send-to-campaign" style="
                            flex: 1;
                            background: #17a2b8;
                            color: white;
                            border: none;
                            padding: 10px 12px;
                            border-radius: 6px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 13px;
                        ">📤 Campaign</button>
                    </div>
                </div>

                <!-- Progress -->
                <div id="progress-section" style="
                    padding: 16px 20px;
                    border-top: 1px solid #e1e5e9;
                    background: #f8f9fa;
                    border-radius: 0 0 12px 12px;
                    display: none;
                ">
                    <div style="font-size: 13px; color: #666; margin-bottom: 8px;">
                        Page <span id="current-page">1</span> of <span id="total-pages">1</span>
                    </div>
                    <div style="
                        background: #e9ecef;
                        height: 6px;
                        border-radius: 3px;
                        overflow: hidden;
                    ">
                        <div id="progress-bar" style="
                            background: linear-gradient(90deg, #28a745, #20c997);
                            height: 100%;
                            width: 0%;
                            transition: width 0.3s;
                        "></div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(collectorUI);
        this.setupEventListeners();
        this.showWelcomeNotification();
    }

    setupEventListeners() {
        // Close button
        document.getElementById('close-collector').addEventListener('click', () => {
            this.closeCollector();
        });

        // Start collection
        document.getElementById('start-collection').addEventListener('click', () => {
            this.startCollection();
        });

        // Stop collection
        document.getElementById('stop-collection').addEventListener('click', () => {
            this.stopCollection();
        });

        // Export profiles
        document.getElementById('export-profiles').addEventListener('click', () => {
            this.exportProfiles();
        });

        // Send to campaign
        document.getElementById('send-to-campaign').addEventListener('click', () => {
            this.sendToCampaign();
        });

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeCollector();
            }
        });
    }

    showWelcomeNotification() {
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="
                position: fixed;
                top: 80px;
                right: 20px;
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
                z-index: 999998;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                font-weight: 500;
                animation: slideIn 0.5s ease-out;
            ">
                🎯 Sales Navigator Collector Ready!
            </div>
            <style>
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            </style>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    closeCollector() {
        this.stopCollection();
        const collector = document.getElementById('sales-nav-collector');
        if (collector) {
            collector.remove();
        }
    }

    async startCollection() {
        this.isCollecting = true;
        this.collectedProfiles = [];
        this.currentPage = 1;
        
        // Update UI
        document.getElementById('start-collection').style.display = 'none';
        document.getElementById('stop-collection').style.display = 'block';
        document.getElementById('progress-section').style.display = 'block';
        document.getElementById('collection-status').textContent = 'Collecting profiles...';
        
        await this.collectFromCurrentPage();
    }

    stopCollection() {
        this.isCollecting = false;
        
        // Update UI
        document.getElementById('start-collection').style.display = 'block';
        document.getElementById('stop-collection').style.display = 'none';
        document.getElementById('collection-status').textContent = `Collection stopped. Found ${this.collectedProfiles.length} profiles.`;
    }

    async collectFromCurrentPage() {
        if (!this.isCollecting) return;
        
        // Get profiles from current page
        const profiles = this.extractProfilesFromPage();
        this.collectedProfiles.push(...profiles);
        
        // Update count
        document.getElementById('profile-count').textContent = this.collectedProfiles.length;
        
        // Check for next page
        const nextButton = document.querySelector('[aria-label="Next"]');
        if (nextButton && !nextButton.disabled && this.isCollecting) {
            this.currentPage++;
            document.getElementById('current-page').textContent = this.currentPage;
            
            // Click next page
            nextButton.click();
            
            // Wait for page load and continue
            setTimeout(() => {
                this.collectFromCurrentPage();
            }, 3000);
        } else {
            this.stopCollection();
        }
    }

    extractProfilesFromPage() {
        const profiles = [];
        const profileElements = document.querySelectorAll('[data-anonymize="person-name"]');
        
        profileElements.forEach(element => {
            const nameElement = element.querySelector('span[aria-hidden="true"]');
            const linkElement = element.closest('a');
            
            if (nameElement && linkElement) {
                profiles.push({
                    name: nameElement.textContent.trim(),
                    url: linkElement.href,
                    timestamp: new Date().toISOString()
                });
            }
        });
        
        return profiles;
    }

    exportProfiles() {
        if (this.collectedProfiles.length === 0) {
            alert('No profiles to export!');
            return;
        }
        
        const csv = this.convertToCSV(this.collectedProfiles);
        this.downloadCSV(csv, 'sales-navigator-profiles.csv');
    }

    convertToCSV(profiles) {
        const headers = ['Name', 'Profile URL', 'Collected At'];
        const rows = profiles.map(profile => [
            profile.name,
            profile.url,
            profile.timestamp
        ]);
        
        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }

    sendToCampaign() {
        if (this.collectedProfiles.length === 0) {
            alert('No profiles to send!');
            return;
        }
        
        // Send to extension popup
        chrome.runtime.sendMessage({
            action: 'addProfilesToCampaign',
            profiles: this.collectedProfiles
        });
        
        alert(`${this.collectedProfiles.length} profiles sent to campaign!`);
    }
}

// Auto-initialize when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new SalesNavigatorCollector();
    });
} else {
    new SalesNavigatorCollector();
}
